package com.radio.rangrez.controller.artist;

import com.radio.rangrez.dto.ArtistDto;
import com.radio.rangrez.model.response.RestResponse;
import com.radio.rangrez.service.artist.ArtistService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RequestMapping("/api/v1/public/artists")
@RestController
public class PublicArtistController {

    @Autowired
    private ArtistService artistService;

    @GetMapping
    public RestResponse getActiveArtists() {
        List<ArtistDto> artists = artistService.getAllArtists();
        return new RestResponse(true, artists);
    }

    @GetMapping("/{id}")
    public RestResponse getArtistById(@PathVariable Long id) {
        ArtistDto artist = artistService.getArtistById(id);
        return new RestResponse(true, artist);
    }
}
