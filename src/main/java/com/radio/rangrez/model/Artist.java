package com.radio.rangrez.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.radio.rangrez.dto.ArtistPreferences;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "artist")
@Entity
public class Artist extends BaseEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "full_name", nullable = false)
    private String fullName;

    @Column(name = "stage_name")
    private String stageName;

    @Column(name = "artist_image_url", nullable = false)
    private String artistImageUrl;

    @Column(name = "biography", columnDefinition = "text")
    private String biography;

    @Column(name = "artist_preferences", columnDefinition = "text")
    private String artistPreferences;

    @Column(name = "country_region", nullable = false)
    private String countryRegion;


}
